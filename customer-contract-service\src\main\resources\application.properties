spring.application.name=customer-contract-service
# PostgreSQL database configuration
spring.datasource.url=***************************************************
spring.datasource.username=postgres
spring.datasource.password=1234
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA configurations
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Server
server.port=8083

# Service URLs
customer.service.url=http://customer-service:8081/api/customer
job.service.url=http://job-service:8082/api/job
job-category.service.url=http://job-service:8082/api/job-category

# DevTools configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s
