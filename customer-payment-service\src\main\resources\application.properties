spring.application.name=customer-payment-service
# PostgreSQL database configuration
spring.datasource.url=**************************************************
spring.datasource.username=postgres
spring.datasource.password=1234
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA configurations
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Server
server.port=8084

# Service URLs
customer.service.url=http://localhost:8081/api/customer
customercontract.service.url=http://localhost:8083/api/customer-contract

# DevTools configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.devtools.restart.poll-interval=2s
spring.devtools.restart.quiet-period=1s
