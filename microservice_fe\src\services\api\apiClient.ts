import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Kiểm tra môi trường và cấu hình URL phù hợp
const getBaseUrl = () => {
  // Nếu đang chạy trong môi trường phát triển (localhost)
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Kiểm tra xem API Gateway có hoạt động không
    const apiGatewayUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080';
    console.log('Using API Gateway URL:', apiGatewayUrl);
    return apiGatewayUrl;
  }

  // Nếu đang chạy trong môi trường production, sử dụng URL tương đối
  return '';
};

// Kiểm tra xem API Gateway có hoạt động không
const checkApiGateway = async (): Promise<boolean> => {
  try {
    const response = await axios.get(`${getBaseUrl()}/actuator/health`, {
      timeout: 3000,
      headers: {
        'Accept': 'application/json'
      }
    });
    return response.status === 200;
  } catch (error) {
    console.error('API Gateway health check failed:', error);
    return false;
  }
};

// Create a base API client instance
const apiClient: AxiosInstance = axios.create({
  baseURL: getBaseUrl(), // API gateway URL
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request
  },
  timeout: 30000, // Tăng timeout lên 30 seconds
  withCredentials: false // Không gửi cookie trong cross-origin requests
});

// Request interceptor for API calls
apiClient.interceptors.request.use(
  (config) => {
    // Log the request for debugging
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for API calls
apiClient.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log(`API Response: ${response.status} ${response.config.url}`);

    // Kiểm tra nếu response là JSON hợp lệ
    try {
      if (typeof response.data === 'string' && response.data.trim() !== '') {
        console.warn('Response is string, attempting to parse as JSON:', response.data);
        response.data = JSON.parse(response.data);
      }
    } catch (e) {
      console.error('Failed to parse response data as JSON:', e);
    }

    return response;
  },
  (error) => {
    // Handle errors globally
    if (axios.isAxiosError(error)) {
      const errorInfo = {
        message: error.message,
        status: error.response?.status,
        url: error.config?.url,
        method: error.config?.method,
        data: error.response?.data,
        code: error.code
      };

      console.error('API Error:', errorInfo);

      // Xử lý thông báo lỗi từ backend
      if (error.response?.data) {
        // Nếu backend trả về thông báo lỗi cụ thể
        if (typeof error.response.data === 'string') {
          error.message = error.response.data;
        } else if (error.response.data.message) {
          error.message = error.response.data.message;
        } else if (error.response.data.error) {
          error.message = error.response.data.error;
        }
      }

      // Chi tiết hơn về các loại lỗi
      if (error.code === 'ECONNABORTED') {
        console.error('Request timeout. The server took too long to respond.');
        error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';
      } else if (error.message.includes('Network Error') || !error.response) {
        console.error('Network error. Please check your connection or the server might be down.');

        // Kiểm tra lỗi CORS
        if (error.message.includes('CORS')) {
          console.error('CORS error detected. This might be a cross-origin issue.');
          error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';
        } else {
          error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';
        }
      } else if (error.response?.status === 404) {
        if (!error.response.data?.message) {
          error.message = 'Không tìm thấy tài nguyên yêu cầu.';
        }
      } else if (error.response?.status === 500) {
        if (!error.response.data?.message) {
          error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';
        }
      } else if (error.response?.status === 403) {
        if (!error.response.data?.message) {
          error.message = 'Bạn không có quyền truy cập tài nguyên này.';
        }
      } else if (error.response?.status === 400) {
        // Lỗi validation hoặc business logic từ backend
        if (!error.response.data?.message) {
          error.message = 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.';
        }
      }
    } else {
      console.error('Unexpected error:', error);
    }
    return Promise.reject(error);
  }
);

// Generic GET request
export const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    // Thêm log để debug
    console.log(`Making GET request to: ${getBaseUrl()}${url}`, config);

    // Thử gọi API qua API Gateway
    try {
      const response: AxiosResponse<T> = await apiClient.get(url, config);
      console.log(`GET request to ${url} succeeded with status:`, response.status);
      return response.data;
    } catch (gatewayError) {
      console.error(`GET request failed through API Gateway for ${url}:`, gatewayError);

      // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại
      let directUrl: string | null = null;

      if (url.startsWith('/api/customer-statistics')) {
        directUrl = `http://localhost:8085${url}`;
        console.log(`Trying direct connection to customer-statistics-service for ${url}`);
      } else if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {
        directUrl = `http://localhost:8083${url}`;
        console.log(`Trying direct connection to customer-contract-service for ${url}`);
      } else if (url.startsWith('/api/customer-payment')) {
        directUrl = `http://localhost:8084${url}`;
        console.log(`Trying direct connection to customer-payment-service for ${url}`);
      } else if (url.startsWith('/api/customer')) {
        directUrl = `http://localhost:8081${url}`;
        console.log(`Trying direct connection to customer-service for ${url}`);
      } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {
        directUrl = `http://localhost:8082${url}`;
        console.log(`Trying direct connection to job-service for ${url}`);
      }

      if (directUrl) {
        console.log(`Direct URL: ${directUrl}`);

        // Gọi trực tiếp đến service
        const directResponse = await axios.get(directUrl, {
          ...config,
          headers: {
            ...config?.headers,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        console.log(`Direct GET request to ${directUrl} succeeded with status:`, directResponse.status);
        return directResponse.data;
      }

      // Nếu không có service phù hợp, ném lỗi
      throw gatewayError;
    }
  } catch (error) {
    console.error(`GET request failed for ${url}:`, error);
    throw error;
  }
};

// Generic POST request
export const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.post(url, data, config);
    return response.data;
  } catch (gatewayError) {
    console.error(`POST request failed through API Gateway for ${url}:`, gatewayError);

    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại
    let directUrl: string | null = null;

    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {
      directUrl = `http://localhost:8083${url}`;
    } else if (url.startsWith('/api/customer-payment')) {
      directUrl = `http://localhost:8084${url}`;
    } else if (url.startsWith('/api/customer')) {
      directUrl = `http://localhost:8081${url}`;
    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {
      directUrl = `http://localhost:8082${url}`;
    }

    if (directUrl) {
      try {
        const directResponse = await axios.post(directUrl, data, {
          ...config,
          headers: {
            ...config?.headers,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        return directResponse.data;
      } catch (directError) {
        // Xử lý lỗi từ direct connection
        if (axios.isAxiosError(directError) && directError.response?.data) {
          if (typeof directError.response.data === 'string') {
            directError.message = directError.response.data;
          } else if (directError.response.data.message) {
            directError.message = directError.response.data.message;
          }
        }
        throw directError;
      }
    }

    throw gatewayError;
  }
};

// Generic PUT request
export const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.put(url, data, config);
    return response.data;
  } catch (gatewayError) {
    console.error(`PUT request failed through API Gateway for ${url}:`, gatewayError);

    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại
    let directUrl: string | null = null;

    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {
      directUrl = `http://localhost:8083${url}`;
    } else if (url.startsWith('/api/customer-payment')) {
      directUrl = `http://localhost:8084${url}`;
    } else if (url.startsWith('/api/customer')) {
      directUrl = `http://localhost:8081${url}`;
    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {
      directUrl = `http://localhost:8082${url}`;
    }

    if (directUrl) {
      const directResponse = await axios.put(directUrl, data, {
        ...config,
        headers: {
          ...config?.headers,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      return directResponse.data;
    }

    throw gatewayError;
  }
};

// Generic DELETE request
export const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.delete(url, config);
    return response.data;
  } catch (gatewayError) {
    console.error(`DELETE request failed through API Gateway for ${url}:`, gatewayError);

    // Thử kết nối trực tiếp đến các service nếu API Gateway thất bại
    let directUrl: string | null = null;

    if (url.startsWith('/api/customer-contract') || url.startsWith('/api/job-detail') || url.startsWith('/api/work-shift')) {
      directUrl = `http://localhost:8083${url}`;
    } else if (url.startsWith('/api/customer-payment')) {
      directUrl = `http://localhost:8084${url}`;
    } else if (url.startsWith('/api/customer')) {
      directUrl = `http://localhost:8081${url}`;
    } else if (url.startsWith('/api/job') || url.startsWith('/api/job-category')) {
      directUrl = `http://localhost:8082${url}`;
    }

    if (directUrl) {
      const directResponse = await axios.delete(directUrl, {
        ...config,
        headers: {
          ...config?.headers,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      return directResponse.data;
    }

    throw gatewayError;
  }
};

export default apiClient;
